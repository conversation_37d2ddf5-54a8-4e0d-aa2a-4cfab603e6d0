// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// UserWithdraws is the golang structure for table user_withdraws.
type UserWithdraws struct {
	UserWithdrawsId        uint            `json:"userWithdrawsId"        orm:"user_withdraws_id"        description:"主键ID"`                                                           // 主键ID
	UserId                 uint64          `json:"userId"                 orm:"user_id"                  description:"用户ID (Foreign key to users table recommended)"`                  // 用户ID (Foreign key to users table recommended)
	TokenId                uint            `json:"tokenId"                orm:"token_id"                 description:"币种ID"`                                                           // 币种ID
	WalletId               string          `json:"walletId"               orm:"wallet_id"                description:"用户钱包ID"`                                                         // 用户钱包ID
	Name                   string          `json:"name"                   orm:"name"                     description:"币种ID"`                                                           // 币种ID
	Chan                   string          `json:"chan"                   orm:"chan"                     description:""`                                                               //
	OrderNo                string          `json:"orderNo"                orm:"order_no"                 description:"提现订单号 (Should be unique)"`                                       // 提现订单号 (Should be unique)
	Address                string          `json:"address"                orm:"address"                  description:"提币目标地址"`                                                         // 提币目标地址
	RecipientName          string          `json:"recipientName"          orm:"recipient_name"           description:"法币收款人姓名"`                                                        // 法币收款人姓名
	RecipientAccount       string          `json:"recipientAccount"       orm:"recipient_account"        description:"法币收款账户"`                                                         // 法币收款账户
	RecipientQrcode        string          `json:"recipientQrcode"        orm:"recipient_qrcode"         description:"法币收款账户"`                                                         // 法币收款账户
	Amount                 decimal.Decimal `json:"amount"                 orm:"amount"                   description:"申请提现金额"`                                                         // 申请提现金额
	HandlingFee            decimal.Decimal `json:"handlingFee"            orm:"handling_fee"             description:"提现手续费"`                                                          // 提现手续费
	ActualAmount           decimal.Decimal `json:"actualAmount"           orm:"actual_amount"            description:"实际到账金额"`                                                         // 实际到账金额
	AuditStatus            uint            `json:"auditStatus"            orm:"audit_status"             description:"审核状态: 1-免审, 2-待审核, 3-审核通过, 4-审核拒绝"`                              // 审核状态: 1-免审, 2-待审核, 3-审核通过, 4-审核拒绝
	AutoWithdrawalProgress uint            `json:"autoWithdrawalProgress" orm:"auto_withdrawal_progress" description:"自动提现状态 0 未开始 1 进行中 2 成功 3 结束"`                                   // 自动提现状态 0 未开始 1 进行中 2 成功 3 结束
	ProcessingStatus       uint            `json:"processingStatus"       orm:"processing_status"        description:"提现处理状态: 1-自动放币处理中, 2-处理中(待冷钱包转入)，3.待人工转账.，4-成功, 5-失败"`           // 提现处理状态: 1-自动放币处理中, 2-处理中(待冷钱包转入)，3.待人工转账.，4-成功, 5-失败
	RefuseReasonZh         string          `json:"refuseReasonZh"         orm:"refuse_reason_zh"         description:"拒绝原因 (中文)"`                                                      // 拒绝原因 (中文)
	RefuseReasonEn         string          `json:"refuseReasonEn"         orm:"refuse_reason_en"         description:"拒绝原因 (英文)"`                                                      // 拒绝原因 (英文)
	TxHash                 string          `json:"txHash"                 orm:"tx_hash"                  description:"链上交易哈希/ID"`                                                      // 链上交易哈希/ID
	ErrorMessage           *gjson.Json     `json:"errorMessage"           orm:"error_message"            description:"失败或错误信息"`                                                        // 失败或错误信息
	UserRemark             string          `json:"userRemark"             orm:"user_remark"              description:"用户提现备注"`                                                         // 用户提现备注
	AdminRemark            string          `json:"adminRemark"            orm:"admin_remark"             description:"管理员审核备注"`                                                        // 管理员审核备注
	CreatedAt              *gtime.Time     `json:"createdAt"              orm:"created_at"               description:"创建时间"`                                                           // 创建时间
	CheckedAt              *gtime.Time     `json:"checkedAt"              orm:"checked_at"               description:"审核时间 (审核通过或拒绝的时间)"`                                              // 审核时间 (审核通过或拒绝的时间)
	ProcessingAt           *gtime.Time     `json:"processingAt"           orm:"processing_at"            description:"开始处理时间 (进入“处理中”状态的时间)"`                                          // 开始处理时间 (进入“处理中”状态的时间)
	CompletedAt            *gtime.Time     `json:"completedAt"            orm:"completed_at"             description:"完成时间 (变为“已完成”或“失败”状态的时间)"`                                       // 完成时间 (变为“已完成”或“失败”状态的时间)
	UpdatedAt              *gtime.Time     `json:"updatedAt"              orm:"updated_at"               description:"最后更新时间"`                                                         // 最后更新时间
	Retries                int             `json:"retries"                orm:"retries"                  description:""`                                                               //
	NergyState             int             `json:"nergyState"             orm:"nergy_state"              description:"0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了"`                                 // 0 表示未发送 1 发送未确认 2 已经确认有能量可以使用了
	NotificationSent       uint            `json:"notificationSent"       orm:"notification_sent"        description:"是否已发送通知: 0-未发送, 1-已发送"`                                          // 是否已发送通知: 0-未发送, 1-已发送
	NotificationSentAt     *gtime.Time     `json:"notificationSentAt"     orm:"notification_sent_at"     description:"通知发送时间"`                                                         // 通知发送时间
	FiatType               string          `json:"fiatType"               orm:"fiat_type"                description:"法币提现类型 alipay_account 支付宝账号  alipay_qr 支付宝二维码  wechat_qr 微信二维码"` // 法币提现类型 alipay_account 支付宝账号  alipay_qr 支付宝二维码  wechat_qr 微信二维码
}
